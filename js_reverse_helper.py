#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript逆向分析辅助工具
专门用于分析抖音bd-ticket-guard-client-data的JavaScript实现
"""

import requests
import re
import base64
import json
from urllib.parse import urljoin

class JSReverseHelper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    def analyze_douyin_js(self):
        """分析抖音网页版的JavaScript代码"""
        print("🔍 开始分析抖音网页版JavaScript...")
        
        try:
            # 访问抖音主页获取JavaScript文件
            response = self.session.get('https://www.douyin.com/')
            html_content = response.text
            
            # 提取JavaScript文件URL
            js_urls = re.findall(r'<script[^>]*src="([^"]*\.js[^"]*)"', html_content)
            
            print(f"📄 找到 {len(js_urls)} 个JavaScript文件")
            
            # 分析每个JS文件
            for i, js_url in enumerate(js_urls[:10]):  # 限制分析前10个文件
                if not js_url.startswith('http'):
                    js_url = urljoin('https://www.douyin.com/', js_url)
                
                print(f"\n📝 分析文件 {i+1}: {js_url}")
                self.analyze_single_js_file(js_url)
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def analyze_single_js_file(self, js_url):
        """分析单个JavaScript文件"""
        try:
            response = self.session.get(js_url, timeout=10)
            js_content = response.text
            
            # 搜索关键字
            keywords = [
                'bd-ticket-guard-client-data',
                'req_sign',
                'signWithHmac',
                'elliptic',
                'ECDH',
                'HMAC',
                'crypto',
                'ticket,path,timestamp'
            ]
            
            found_keywords = []
            for keyword in keywords:
                if keyword in js_content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"  🎯 找到关键字: {', '.join(found_keywords)}")
                
                # 提取相关代码片段
                self.extract_relevant_code(js_content, found_keywords)
            else:
                print("  ❌ 未找到相关关键字")
                
        except Exception as e:
            print(f"  ⚠️ 分析失败: {e}")
    
    def extract_relevant_code(self, js_content, keywords):
        """提取相关的代码片段"""
        print("  📋 提取相关代码片段:")
        
        for keyword in keywords:
            # 查找包含关键字的行及其上下文
            lines = js_content.split('\n')
            for i, line in enumerate(lines):
                if keyword in line:
                    # 提取上下文（前后5行）
                    start = max(0, i - 5)
                    end = min(len(lines), i + 6)
                    context = lines[start:end]
                    
                    print(f"    🔍 关键字 '{keyword}' 上下文:")
                    for j, context_line in enumerate(context):
                        line_num = start + j + 1
                        marker = ">>> " if j == (i - start) else "    "
                        print(f"    {marker}{line_num:4d}: {context_line.strip()}")
                    print()
                    break
    
    def search_hmac_patterns(self):
        """搜索HMAC相关的模式"""
        print("\n🔐 搜索HMAC签名模式...")
        
        # 常见的HMAC实现模式
        patterns = [
            r'hmac\s*\(\s*[^,]+,\s*[^)]+\)',
            r'signWithHmac\s*\([^)]+\)',
            r'crypto\.createHmac\s*\([^)]+\)',
            r'CryptoJS\.HmacSHA256\s*\([^)]+\)',
            r'\.digest\s*\(\s*["\']base64["\']\s*\)',
        ]
        
        # 这里可以添加具体的搜索逻辑
        print("  💡 建议手动搜索以下模式:")
        for i, pattern in enumerate(patterns, 1):
            print(f"    {i}. {pattern}")
    
    def generate_js_hook_code(self):
        """生成JavaScript Hook代码用于浏览器调试"""
        hook_code = '''
// 抖音req_sign逆向Hook代码
// 在浏览器控制台中运行此代码来拦截关键函数

console.log("🚀 开始Hook抖音签名算法...");

// Hook HMAC相关函数
if (window.crypto && window.crypto.subtle) {
    const originalSign = window.crypto.subtle.sign;
    window.crypto.subtle.sign = function(...args) {
        console.log("🔐 crypto.subtle.sign 被调用:", args);
        return originalSign.apply(this, args);
    };
}

// Hook可能的签名函数
const hookFunction = (obj, funcName) => {
    if (obj && obj[funcName]) {
        const original = obj[funcName];
        obj[funcName] = function(...args) {
            console.log(`🎯 ${funcName} 被调用:`, args);
            const result = original.apply(this, args);
            console.log(`📤 ${funcName} 返回:`, result);
            return result;
        };
    }
};

// 尝试Hook常见的函数名
['signWithHmac', 'sign', 'hmac', 'digest'].forEach(funcName => {
    // 在全局对象中搜索
    for (let key in window) {
        try {
            if (window[key] && typeof window[key] === 'object') {
                hookFunction(window[key], funcName);
            }
        } catch (e) {}
    }
});

// Hook Base64编码
const originalBtoa = window.btoa;
window.btoa = function(str) {
    if (str.includes('req_sign') || str.includes('bd-ticket-guard')) {
        console.log("📋 Base64编码被调用:", str);
    }
    return originalBtoa.call(this, str);
};

// 监听网络请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const [url, options] = args;
    if (url.includes('douyin.com') || url.includes('aweme')) {
        console.log("🌐 网络请求:", url, options);
    }
    return originalFetch.apply(this, args);
};

console.log("✅ Hook代码已安装，请执行相关操作触发签名算法");
'''
        
        print("\n📋 生成的JavaScript Hook代码:")
        print("=" * 60)
        print(hook_code)
        print("=" * 60)
        
        # 保存到文件
        with open('douyin_hook.js', 'w', encoding='utf-8') as f:
            f.write(hook_code)
        print("💾 Hook代码已保存到 douyin_hook.js")
    
    def analyze_elliptic_curve_patterns(self):
        """分析椭圆曲线相关的模式"""
        print("\n🔄 分析椭圆曲线算法模式...")
        
        ec_patterns = [
            "椭圆曲线密钥生成",
            "ECDH密钥交换", 
            "密钥派生函数",
            "哈希拼接操作"
        ]
        
        print("  🔍 需要重点关注的椭圆曲线模式:")
        for i, pattern in enumerate(ec_patterns, 1):
            print(f"    {i}. {pattern}")
        
        print("\n  💡 建议的分析步骤:")
        print("    1. 在浏览器开发者工具中设置断点")
        print("    2. 搜索包含'elliptic'、'curve'、'ECDH'的代码")
        print("    3. 分析密钥生成的具体参数")
        print("    4. 跟踪r值的计算过程")

if __name__ == "__main__":
    helper = JSReverseHelper()
    
    print("🔧 JavaScript逆向分析工具")
    print("=" * 50)
    
    # 生成Hook代码
    helper.generate_js_hook_code()
    
    # 分析椭圆曲线模式
    helper.analyze_elliptic_curve_patterns()
    
    # 搜索HMAC模式
    helper.search_hmac_patterns()
    
    print("\n🎯 下一步建议:")
    print("1. 在浏览器中打开抖音网页版")
    print("2. 运行生成的Hook代码 (douyin_hook.js)")
    print("3. 执行点赞等操作触发签名算法")
    print("4. 观察控制台输出，定位关键函数")
    print("5. 分析椭圆曲线密钥生成过程")
