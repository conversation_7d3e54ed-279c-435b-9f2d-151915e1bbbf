#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音bd-ticket-guard-client-data中req_sign逆向分析工具
作者: AI Assistant
日期: 2025-07-31
"""

import base64
import json
import hashlib
import hmac
import time
from datetime import datetime

class ReqSignAnalyzer:
    def __init__(self):
        self.data = None
        self.load_sample_data()
    
    def load_sample_data(self):
        """加载result.txt中的样本数据"""
        try:
            with open('result.txt', 'r') as f:
                base64_data = f.readline().strip()
            
            # 解码Base64
            decoded_data = base64.b64decode(base64_data).decode('utf-8')
            self.data = json.loads(decoded_data)
            print("✅ 样本数据加载成功")
            self.analyze_sample_data()
        except Exception as e:
            print(f"❌ 加载样本数据失败: {e}")
    
    def analyze_sample_data(self):
        """分析样本数据结构"""
        print("\n📊 样本数据分析:")
        print("-" * 50)
        
        for key, value in self.data.items():
            print(f"{key}: {value}")
            
            if key == 'req_sign':
                # 分析req_sign
                req_sign_bytes = base64.b64decode(value)
                print(f"  -> 解码后字节长度: {len(req_sign_bytes)} bytes")
                print(f"  -> 十六进制: {req_sign_bytes.hex()}")
                print(f"  -> 可能的算法: HMAC-SHA256 (32字节输出)")
            
            elif key == 'timestamp':
                # 分析时间戳
                dt = datetime.fromtimestamp(value)
                print(f"  -> 时间: {dt}")
        
        print("-" * 50)
    
    def construct_plaintext_candidates(self, path="/aweme/v1/web/commit/item/digg/"):
        """构造可能的明文字符串"""
        timestamp = self.data['timestamp']
        
        # 根据技术文档，明文格式为: ticket=&path=[路径]&timestamp=[时间戳]
        candidates = [
            f"ticket=&path={path}&timestamp={timestamp}",
            f"ticket,path,timestamp",  # req_content的值
            f"ticket={{}}&path={path}&timestamp={timestamp}",  # ticket可能为空
            f"path={path}&timestamp={timestamp}",  # 可能不包含ticket
            f"ticket&path={path}&timestamp={timestamp}",  # 不同的分隔符
        ]
        
        print("\n🔍 可能的明文字符串:")
        for i, candidate in enumerate(candidates, 1):
            print(f"{i}. {candidate}")
        
        return candidates
    
    def test_hmac_with_keys(self, plaintext, test_keys):
        """使用不同密钥测试HMAC"""
        target_signature = base64.b64decode(self.data['req_sign'])
        
        print(f"\n🔐 测试HMAC签名 (目标: {target_signature.hex()})")
        print("-" * 60)
        
        for i, key in enumerate(test_keys, 1):
            if isinstance(key, str):
                key = key.encode('utf-8')
            
            # 测试不同的HMAC算法
            algorithms = ['sha256', 'sha1', 'md5', 'sha512']
            
            for alg in algorithms:
                try:
                    signature = hmac.new(key, plaintext.encode('utf-8'), getattr(hashlib, alg)).digest()
                    
                    if signature == target_signature:
                        print(f"🎯 找到匹配! 密钥{i}, 算法: HMAC-{alg.upper()}")
                        print(f"   密钥: {key}")
                        print(f"   明文: {plaintext}")
                        return True
                    else:
                        print(f"❌ 密钥{i} + {alg}: {signature.hex()[:16]}...")
                except Exception as e:
                    print(f"⚠️  密钥{i} + {alg}: 错误 - {e}")
        
        return False
    
    def generate_test_keys(self):
        """生成测试用的密钥"""
        # 基于技术文档，密钥可能来源于椭圆曲线算法生成的r值
        test_keys = [
            # 常见的测试密钥
            "secret",
            "key",
            "hmac_key",
            "bd_ticket_guard",
            
            # 可能从ts_sign派生的密钥
            self.data['ts_sign'],
            self.data['ts_sign'].split('.')[2] if '.' in self.data['ts_sign'] else "",
            
            # 时间戳相关
            str(self.data['timestamp']),
            str(self.data['timestamp'])[:10],  # 前10位
            
            # 组合密钥
            f"bd_ticket_guard_{self.data['timestamp']}",
            f"req_sign_{self.data['timestamp']}",
        ]
        
        return [key for key in test_keys if key]  # 过滤空字符串
    
    def run_analysis(self):
        """运行完整的分析流程"""
        print("🚀 开始req_sign逆向分析")
        print("=" * 60)
        
        if not self.data:
            print("❌ 没有可用的样本数据")
            return
        
        # 1. 构造明文候选
        plaintexts = self.construct_plaintext_candidates()
        
        # 2. 生成测试密钥
        test_keys = self.generate_test_keys()
        print(f"\n🔑 生成了 {len(test_keys)} 个测试密钥")
        
        # 3. 暴力测试
        found = False
        for plaintext in plaintexts:
            print(f"\n📝 测试明文: {plaintext}")
            if self.test_hmac_with_keys(plaintext, test_keys):
                found = True
                break
        
        if not found:
            print("\n❌ 未找到匹配的HMAC组合")
            print("💡 建议:")
            print("   1. 需要获取真实的HMAC密钥(r值)")
            print("   2. 分析JavaScript代码中的椭圆曲线算法")
            print("   3. 检查明文格式是否正确")

if __name__ == "__main__":
    analyzer = ReqSignAnalyzer()
    analyzer.run_analysis()
