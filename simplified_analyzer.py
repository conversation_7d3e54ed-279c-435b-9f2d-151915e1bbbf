#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版req_sign分析工具
专注于分析现有数据和生成测试密钥
"""

import base64
import json
import hashlib
import hmac
import secrets

class SimplifiedAnalyzer:
    def __init__(self):
        self.load_sample_data()
        
    def load_sample_data(self):
        """加载样本数据"""
        with open('result.txt', 'r') as f:
            base64_data = f.readline().strip()
        decoded_data = base64.b64decode(base64_data).decode('utf-8')
        self.data = json.loads(decoded_data)
        print("✅ 样本数据加载成功")
        
    def analyze_ts_sign_structure(self):
        """深入分析ts_sign的结构"""
        ts_sign = self.data['ts_sign']
        print(f"\n🔍 深入分析ts_sign结构:")
        print(f"完整ts_sign: {ts_sign}")
        
        parts = ts_sign.split('.')
        if len(parts) >= 3:
            prefix = parts[0]  # "ts"
            version = parts[1]  # "2"
            hex_data = parts[2]
            
            print(f"前缀: {prefix}")
            print(f"版本: {version}")
            print(f"十六进制数据: {hex_data}")
            print(f"十六进制长度: {len(hex_data)} 字符 = {len(hex_data)//2} 字节")
            
            # 解析十六进制数据
            try:
                raw_bytes = bytes.fromhex(hex_data)
                print(f"原始字节长度: {len(raw_bytes)}")
                
                # 分析可能的密钥结构
                self.analyze_hex_data_structure(raw_bytes)
                
                return raw_bytes
            except ValueError as e:
                print(f"❌ 十六进制解析失败: {e}")
                return None
    
    def analyze_hex_data_structure(self, raw_bytes):
        """分析十六进制数据的可能结构"""
        print(f"\n📊 分析 {len(raw_bytes)} 字节的数据结构:")
        
        # 按32字节分段分析
        chunk_size = 32
        for i in range(0, len(raw_bytes), chunk_size):
            chunk = raw_bytes[i:i+chunk_size]
            print(f"第{i//chunk_size + 1}段 (字节{i}-{i+len(chunk)-1}): {chunk.hex()}")
            
            # 分析这段数据的特征
            if len(chunk) == 32:
                print(f"  -> 可能是: SHA256哈希值、HMAC密钥、椭圆曲线私钥")
            elif len(chunk) == 33:
                print(f"  -> 可能是: 压缩的椭圆曲线公钥")
            elif len(chunk) == 65:
                print(f"  -> 可能是: 未压缩的椭圆曲线公钥")
    
    def generate_candidate_keys_from_ts_sign(self):
        """从ts_sign生成候选密钥"""
        print(f"\n🔑 从ts_sign生成候选密钥:")
        
        ts_bytes = self.analyze_ts_sign_structure()
        if not ts_bytes:
            return []
        
        candidate_keys = []
        
        # 1. 直接使用不同长度的片段
        if len(ts_bytes) >= 32:
            candidate_keys.append(("前32字节", ts_bytes[:32]))
        
        if len(ts_bytes) >= 64:
            candidate_keys.append(("后32字节", ts_bytes[32:64]))
            candidate_keys.append(("中间32字节", ts_bytes[16:48]))
        
        # 2. 哈希变换
        candidate_keys.append(("SHA256(全部)", hashlib.sha256(ts_bytes).digest()))
        candidate_keys.append(("MD5(全部)", hashlib.md5(ts_bytes).digest()))
        
        if len(ts_bytes) >= 32:
            candidate_keys.append(("SHA256(前32字节)", hashlib.sha256(ts_bytes[:32]).digest()))
        
        # 3. 组合变换
        timestamp_bytes = str(self.data['timestamp']).encode('utf-8')
        candidate_keys.append(("SHA256(ts_bytes + timestamp)", hashlib.sha256(ts_bytes + timestamp_bytes).digest()))
        
        # 4. 常见的密钥派生
        for salt in [b'bd-ticket-guard', b'req_sign', b'douyin', b'']:
            key = hashlib.pbkdf2_hmac('sha256', ts_bytes, salt, 1000, 32)
            candidate_keys.append((f"PBKDF2(salt={salt})", key))
        
        print(f"生成了 {len(candidate_keys)} 个候选密钥:")
        for i, (name, key) in enumerate(candidate_keys, 1):
            print(f"  {i:2d}. {name}: {key.hex()[:32]}...")
        
        return candidate_keys
    
    def test_all_combinations(self):
        """测试所有可能的组合"""
        print(f"\n🧪 测试所有可能的组合:")
        
        # 生成候选密钥
        candidate_keys = self.generate_candidate_keys_from_ts_sign()
        
        # 生成候选明文
        timestamp = self.data['timestamp']
        candidate_plaintexts = [
            f"ticket=&path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}",
            f"ticket,path,timestamp",
            f"path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}",
            f"ticket&path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}",
            f"ticket=&path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}&",
            f"/aweme/v1/web/commit/item/digg/{timestamp}",
            f"bd-ticket-guard-client-data{timestamp}",
        ]
        
        target_signature = base64.b64decode(self.data['req_sign'])
        print(f"目标签名: {target_signature.hex()}")
        
        total_tests = len(candidate_keys) * len(candidate_plaintexts)
        print(f"总共测试: {len(candidate_keys)} 密钥 × {len(candidate_plaintexts)} 明文 = {total_tests} 组合")
        
        # 测试所有组合
        for key_idx, (key_name, key) in enumerate(candidate_keys, 1):
            for text_idx, plaintext in enumerate(candidate_plaintexts, 1):
                # 测试不同的HMAC算法
                for alg_name, alg_func in [('SHA256', hashlib.sha256), ('SHA1', hashlib.sha1), ('MD5', hashlib.md5)]:
                    try:
                        signature = hmac.new(key, plaintext.encode('utf-8'), alg_func).digest()
                        
                        if signature == target_signature:
                            print(f"\n🎯 找到匹配!")
                            print(f"   密钥: {key_name}")
                            print(f"   密钥值: {key.hex()}")
                            print(f"   明文: {plaintext}")
                            print(f"   算法: HMAC-{alg_name}")
                            print(f"   签名: {signature.hex()}")
                            return True
                        
                        # 显示进度（每100次测试显示一次）
                        test_num = (key_idx - 1) * len(candidate_plaintexts) * 3 + (text_idx - 1) * 3 + (['SHA256', 'SHA1', 'MD5'].index(alg_name) + 1)
                        if test_num % 50 == 0:
                            print(f"  进度: {test_num}/{total_tests * 3} ({test_num/(total_tests*3)*100:.1f}%)")
                            
                    except Exception as e:
                        print(f"  ⚠️ 测试失败: 密钥{key_idx} + 明文{text_idx} + {alg_name}: {e}")
        
        print(f"\n❌ 测试完成，未找到匹配的组合")
        return False
    
    def analyze_req_sign_properties(self):
        """分析req_sign的属性"""
        print(f"\n🔬 分析req_sign的属性:")
        
        req_sign_b64 = self.data['req_sign']
        req_sign_bytes = base64.b64decode(req_sign_b64)
        
        print(f"Base64: {req_sign_b64}")
        print(f"十六进制: {req_sign_bytes.hex()}")
        print(f"字节长度: {len(req_sign_bytes)}")
        
        # 分析字节分布
        print(f"字节值分布:")
        byte_counts = {}
        for byte in req_sign_bytes:
            byte_counts[byte] = byte_counts.get(byte, 0) + 1
        
        for byte_val, count in sorted(byte_counts.items()):
            print(f"  0x{byte_val:02x}: {count} 次")
        
        # 检查是否符合常见哈希算法的输出特征
        if len(req_sign_bytes) == 32:
            print("✅ 长度符合 SHA256/HMAC-SHA256 输出")
        elif len(req_sign_bytes) == 20:
            print("✅ 长度符合 SHA1/HMAC-SHA1 输出")
        elif len(req_sign_bytes) == 16:
            print("✅ 长度符合 MD5/HMAC-MD5 输出")
        else:
            print(f"⚠️ 长度 {len(req_sign_bytes)} 不符合常见哈希算法")
    
    def suggest_next_steps(self):
        """建议下一步的分析方向"""
        print(f"\n💡 建议下一步的分析方向:")
        print("1. 🌐 浏览器逆向:")
        print("   - 在浏览器中打开抖音网页版")
        print("   - 运行 douyin_hook.js 中的Hook代码")
        print("   - 执行点赞操作，观察控制台输出")
        
        print("\n2. 🔍 JavaScript代码分析:")
        print("   - 搜索包含 'signWithHmac' 的JavaScript文件")
        print("   - 查找椭圆曲线相关的代码 (elliptic, ECDH)")
        print("   - 分析 ke 和 Ft 方法的具体实现")
        
        print("\n3. 🔐 密钥生成分析:")
        print("   - 分析 r值 的具体生成算法")
        print("   - 确定椭圆曲线的参数 (曲线类型、基点等)")
        print("   - 理解密钥派生的具体步骤")
        
        print("\n4. 📱 移动端分析:")
        print("   - 如果网页版分析困难，可以考虑分析移动端APP")
        print("   - 使用 Frida 等工具Hook移动端的加密函数")

if __name__ == "__main__":
    analyzer = SimplifiedAnalyzer()
    
    print("🔧 简化版req_sign分析工具")
    print("=" * 50)
    
    # 分析req_sign属性
    analyzer.analyze_req_sign_properties()
    
    # 分析ts_sign结构
    analyzer.analyze_ts_sign_structure()
    
    # 测试所有组合
    found = analyzer.test_all_combinations()
    
    if not found:
        # 建议下一步
        analyzer.suggest_next_steps()
