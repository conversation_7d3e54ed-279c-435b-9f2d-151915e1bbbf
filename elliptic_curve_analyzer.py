#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
椭圆曲线密钥分析工具
专门用于分析抖音req_sign中的椭圆曲线密钥生成算法
"""

import hashlib
import hmac
import base64
import json
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
import secrets

class EllipticCurveAnalyzer:
    def __init__(self):
        self.load_sample_data()
        
    def load_sample_data(self):
        """加载样本数据"""
        with open('result.txt', 'r') as f:
            base64_data = f.readline().strip()
        decoded_data = base64.b64decode(base64_data).decode('utf-8')
        self.data = json.loads(decoded_data)
        
    def analyze_ts_sign(self):
        """分析ts_sign的结构"""
        ts_sign = self.data['ts_sign']
        print("🔍 分析ts_sign结构:")
        print(f"完整ts_sign: {ts_sign}")
        
        # ts_sign格式: ts.2.{hex_data}
        parts = ts_sign.split('.')
        if len(parts) >= 3:
            version = parts[1]
            hex_data = parts[2]
            
            print(f"版本: {version}")
            print(f"十六进制数据长度: {len(hex_data)} 字符")
            print(f"对应字节长度: {len(hex_data) // 2} 字节")
            
            # 尝试解析十六进制数据
            try:
                raw_bytes = bytes.fromhex(hex_data)
                print(f"原始字节: {raw_bytes[:32].hex()}...")
                
                # 分析可能的结构
                self.analyze_possible_key_structure(raw_bytes)
                
            except ValueError as e:
                print(f"❌ 十六进制解析失败: {e}")
    
    def analyze_possible_key_structure(self, raw_bytes):
        """分析可能的密钥结构"""
        print("\n🔐 分析可能的密钥结构:")
        
        # 常见的椭圆曲线密钥长度
        key_lengths = {
            32: "P-256 私钥或共享密钥",
            33: "P-256 压缩公钥", 
            65: "P-256 未压缩公钥",
            48: "P-384 私钥",
            64: "P-521 私钥或SHA-512哈希"
        }
        
        total_len = len(raw_bytes)
        print(f"总长度: {total_len} 字节")
        
        if total_len in key_lengths:
            print(f"可能类型: {key_lengths[total_len]}")
        
        # 尝试分段分析
        if total_len >= 64:
            print("\n📊 分段分析:")
            print(f"前32字节: {raw_bytes[:32].hex()}")
            print(f"后32字节: {raw_bytes[32:64].hex()}")
            
            if total_len > 64:
                print(f"剩余字节: {raw_bytes[64:].hex()}")
    
    def generate_test_ecdh_keys(self):
        """生成测试用的ECDH密钥对"""
        print("\n🔑 生成测试ECDH密钥对:")
        
        # 使用P-256曲线（最常用）
        private_key = ec.generate_private_key(ec.SECP256R1())
        public_key = private_key.public_key()
        
        # 序列化密钥
        private_value = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.X962,
            format=serialization.PublicFormat.UncompressedPoint
        )
        
        print(f"私钥 (32字节): {private_value.hex()}")
        print(f"公钥 (65字节): {public_bytes.hex()}")
        
        return private_key, public_key
    
    def simulate_ecdh_key_derivation(self):
        """模拟ECDH密钥派生过程"""
        print("\n🔄 模拟ECDH密钥派生过程:")
        
        # 生成两个密钥对（模拟客户端和服务器）
        client_private = ec.generate_private_key(ec.SECP256R1())
        server_private = ec.generate_private_key(ec.SECP256R1())
        
        client_public = client_private.public_key()
        server_public = server_private.public_key()
        
        # 执行ECDH
        client_shared = client_private.exchange(ec.ECDH(), server_public)
        server_shared = server_private.exchange(ec.ECDH(), client_public)
        
        print(f"客户端共享密钥: {client_shared.hex()}")
        print(f"服务器共享密钥: {server_shared.hex()}")
        print(f"密钥是否一致: {client_shared == server_shared}")
        
        # 使用HKDF派生最终密钥
        derived_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=None,
            info=b'bd-ticket-guard',
        ).derive(client_shared)
        
        print(f"派生密钥: {derived_key.hex()}")
        
        return derived_key
    
    def test_hmac_with_derived_keys(self):
        """使用派生的密钥测试HMAC"""
        print("\n🧪 使用派生密钥测试HMAC:")
        
        # 生成多个测试密钥
        test_keys = []
        
        # 1. 模拟ECDH派生
        for i in range(5):
            derived_key = self.simulate_ecdh_key_derivation()
            test_keys.append(derived_key)
        
        # 2. 从ts_sign派生
        ts_sign = self.data['ts_sign']
        if '.' in ts_sign:
            hex_part = ts_sign.split('.')[2]
            try:
                ts_bytes = bytes.fromhex(hex_part)
                # 尝试不同的截取方式
                test_keys.extend([
                    ts_bytes[:32],  # 前32字节
                    ts_bytes[32:64] if len(ts_bytes) >= 64 else ts_bytes,  # 后32字节
                    hashlib.sha256(ts_bytes).digest(),  # SHA256哈希
                ])
            except:
                pass
        
        # 测试明文
        timestamp = self.data['timestamp']
        plaintexts = [
            f"ticket=&path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}",
            f"ticket,path,timestamp",
            f"path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}",
        ]
        
        target_signature = base64.b64decode(self.data['req_sign'])
        
        print(f"目标签名: {target_signature.hex()}")
        print(f"测试 {len(test_keys)} 个密钥 × {len(plaintexts)} 个明文...")
        
        for i, key in enumerate(test_keys):
            for j, plaintext in enumerate(plaintexts):
                signature = hmac.new(key, plaintext.encode('utf-8'), hashlib.sha256).digest()
                
                if signature == target_signature:
                    print(f"🎯 找到匹配! 密钥{i+1}, 明文{j+1}")
                    print(f"   密钥: {key.hex()}")
                    print(f"   明文: {plaintext}")
                    return True
        
        print("❌ 未找到匹配的组合")
        return False
    
    def analyze_r_value_generation(self):
        """分析r值生成过程"""
        print("\n🎲 分析r值生成过程:")
        
        print("根据技术文档，r值生成包括以下步骤:")
        print("1. 提取公钥")
        print("2. 创建椭圆曲线对象和提取私钥")
        print("3. 密钥派生")
        print("4. 哈希和拼接操作")
        
        # 模拟可能的r值生成过程
        print("\n🔬 模拟r值生成:")
        
        # 步骤1: 生成椭圆曲线密钥对
        private_key = ec.generate_private_key(ec.SECP256R1())
        public_key = private_key.public_key()
        
        # 步骤2: 提取密钥字节
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        # 提取原始私钥值
        private_value = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.X962,
            format=serialization.PublicFormat.UncompressedPoint
        )
        
        # 步骤3: 哈希和拼接
        combined = private_value + public_bytes
        r_value = hashlib.sha256(combined).digest()
        
        print(f"模拟r值: {r_value.hex()}")
        
        # 测试这个r值
        timestamp = self.data['timestamp']
        plaintext = f"ticket=&path=/aweme/v1/web/commit/item/digg/&timestamp={timestamp}"
        signature = hmac.new(r_value, plaintext.encode('utf-8'), hashlib.sha256).digest()
        
        target_signature = base64.b64decode(self.data['req_sign'])
        
        print(f"测试签名: {signature.hex()}")
        print(f"目标签名: {target_signature.hex()}")
        print(f"是否匹配: {signature == target_signature}")

if __name__ == "__main__":
    analyzer = EllipticCurveAnalyzer()
    
    print("🔄 椭圆曲线密钥分析工具")
    print("=" * 50)
    
    # 分析ts_sign
    analyzer.analyze_ts_sign()
    
    # 分析r值生成
    analyzer.analyze_r_value_generation()
    
    # 测试HMAC
    analyzer.test_hmac_with_derived_keys()
